package com.example.chxmap;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.webkit.GeolocationPermissions;
import android.webkit.SslErrorHandler;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebStorage;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
//import com.amap.api.location.AMapLocation;
//import com.amap.api.location.AMapLocationClient;
//import com.amap.api.location.AMapLocationClientOption;
//import com.amap.api.location.AMapLocationListener;
//import com.amap.api.maps.AMap;
//import com.amap.api.maps.CameraUpdateFactory;
//import com.amap.api.maps.MapView;
//import com.amap.api.maps.model.MyLocationStyle;
//import com.amap.api.maps.model.Text;
//import com.amap.api.services.core.ServiceSettings;


public class MainActivity extends AppCompatActivity {
    private static final int LOCATION_PERMISSION_REQUEST_CODE = 1;
    private WebView webView;

//    MapView mMapView = null;
//    Button button;
//    double jd = 0;//经度
//    double wd = 0;//纬度
    //声明AMapLocationClient类对象
//    public AMapLocationClient mLocationClient = null;
    //声明AMapLocationClientOption对象
//    public AMapLocationClientOption mLocationOption = null;
    //声明定位回调监听器
//    public AMapLocationListener mLocationListener = new AMapLocationListener() {
//        @Override
//        public void onLocationChanged(AMapLocation amapLocation) {
//            TextView tx = findViewById(R.id.textView2);
//            if (amapLocation != null) {
//                if (amapLocation.getErrorCode() == 0) {
//                    jd = amapLocation.getLatitude();
//                    wd = amapLocation.getLongitude();
    ////                    tx.setText("经度：" + amapLocation.getLatitude() +  //获取纬度
    ////                            "纬度：" + amapLocation.getLongitude() +//获取经度
//                    tx.setText( "定位精度：" + amapLocation.getAccuracy() );//获取精度信息
//                    Log.e(  "AmapError" ,"经度：" + amapLocation.getLatitude() +  //获取纬度
//                            "纬度：" + amapLocation.getLongitude() +//获取经度
//                            "精度：" + amapLocation.getAccuracy() //获取精度信息
//                    );
//                    //可在其中解析amapLocation获取相应内容。
//                }else {
//                    tx.setText("失败" + "location Error, ErrCode:"
//                            + amapLocation.getErrorCode() + ", errInfo:"
//                            + amapLocation.getErrorInfo());
//                    //定位失败时，可通过ErrCode（错误码）信息来确定失败的原因，errInfo是错误信息，详见错误码表。
//                    Log.e("AmapError","location Error, ErrCode:"
//                            + amapLocation.getErrorCode() + ", errInfo:"
//                            + amapLocation.getErrorInfo());
//                }
//            }
//        }
//    };

    private String sharedPreferencesKey = "webview_prefs";
    private boolean isFirstLaunch = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        webView = (WebView) findViewById(R.id.wv);
        WebSettings wSettings;

        wSettings = webView.getSettings();

        // 只在首次安装或需要时清除缓存，避免每次都清除
        if (shouldClearCache()) {
            webView.clearCache(true);
            WebStorage.getInstance().deleteAllData();
            markCacheCleared();
        }

        // 优化WebView性能设置
        setupWebViewOptimization(wSettings);
//        webview.setClickable(true);
//        webview.setFocusableInTouchMode(true);
        // 启用javascript
        wSettings.setJavaScriptEnabled(true);
        wSettings.setGeolocationEnabled(true);
        // 开启 DOM storage 功能
        wSettings.setDomStorageEnabled(true);
        // 开启数据库存储功能
        wSettings.setDatabaseEnabled(true);
        // 设置缓存模式
        wSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
// 如果你的网站包含混合内容（http和https混合），需要设置允许加载混合内容
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webView.getSettings().setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        // 设置沉浸式全屏模式
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            View decorView = getWindow().getDecorView();
            int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;
            decorView.setSystemUiVisibility(uiOptions);
        }
        // 设置允许JS弹窗
        wSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        wSettings.setDefaultTextEncodingName("GBK");
//        webview.loadUrl("http://**************:8000/mymap2/");

        String zy17w = "https://************:19820/";//
        // String zy17w = "http://************:19810/";//

        //   String zy17w = "http://**************:9004/";//

//       webview.loadUrl("http://***********:3000/");//调试

        // 设置 WebChromeClient 处理地理位置权限请求
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onGeolocationPermissionsShowPrompt(String origin, GeolocationPermissions.Callback callback) {
                if (ContextCompat.checkSelfPermission(MainActivity.this, Manifest.permission.ACCESS_FINE_LOCATION)
                        == PackageManager.PERMISSION_GRANTED) {
                    // 如果已经授予权限，允许使用地理位置
                    callback.invoke(origin, true, false);
                } else {
                    // 请求权限
                    ActivityCompat.requestPermissions(MainActivity.this,
                            new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                            LOCATION_PERMISSION_REQUEST_CODE);
                }
            }

        });


        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                // 忽略SSL错误，接受所有证书（不建议在生产环境中使用）
                handler.proceed();
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                if (url.startsWith("androidamap") || url.contains("amap")) {
                    handleAmapNavigation(url);
                    return true;  // 拦截URL
                }
                return super.shouldOverrideUrlLoading(view, url);
            }
        });

        webView.loadUrl(zy17w);

//        button = (Button) findViewById(R.id.button);
//
//        button.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                // 通过Handler发送消息
//                webview.post(new Runnable() {
//                    @Override
//                    public void run() {
//
//                        // 注意调用的JS方法名要对应上
//                        // 调用javascript的callJS()方法
//                        String jdstr =  jd+"";
//                        String wdstr =  wd+"";
//                        String urlstr = String.format("javascript:callJS('%s,%s')",jdstr,wdstr);
//                        webview.loadUrl(urlstr);
//                    }
//                });
//
//            }
//        });
        //刷新按钮
//        Button btnsx = (Button) findViewById(R.id.sx);
//
//        btnsx.setOnClickListener(new View.OnClickListener() {
//                 public void onClick(View v) {
//                     Intent intent = new Intent(MainActivity.this, MainActivity.class);
//                     startActivity(intent);
//                 }
//             });
// 由于设置了弹窗检验调用结果,所以需要支持js对话框
        // webview只是载体，内容的渲染需要使用webviewChromClient类去实现
        // 通过设置WebChromeClient对象处理JavaScript的对话框
        //设置响应js 的Alert()函数
//        webview.setLongClickable(true);
//        webview.setOnLongClickListener(new View.OnLongClickListener() {
//            @Override
//            public boolean onLongClick(View v) {
//                Toast.makeText(MainActivity.this, "提示的内容", Toast.LENGTH_LONG).show();
//                return true;
//            }
//        });
//        webview.setWebChromeClient(new WebChromeClient() {
//        @Override
//        public boolean onJsAlert(WebView view, String url, String message, final JsResult result) {
//            AlertDialog.Builder b = new AlertDialog.Builder(MainActivity.this);
//            b.setTitle("Alert");
//            b.setMessage(message);
//            b.setPositiveButton(android.R.string.ok, new DialogInterface.OnClickListener() {
//                @Override
//                public void onClick(DialogInterface dialog, int which) {
//                    result.confirm();
//                }
//            });
//            b.setCancelable(false);
//            b.create().show();
//            return true;
//        }
//
//        });
//
//        try{
//            ServiceSettings.updatePrivacyShow(this, true, true);
//            ServiceSettings.updatePrivacyAgree(this,true);
//        } catch (Exception e){
//            e.printStackTrace();
//        }

//
//        //获取地图控件引用
//        mMapView = (MapView) findViewById(R.id.map);
//        //在activity执行onCreate时执行mMapView.onCreate(savedInstanceState)，创建地图
//        mMapView.onCreate(savedInstanceState);// 此方法必须重写
//        AMap aMap = mMapView.getMap();
////
//////        aMap.setTrafficEnabled(true);// 显示实时交通状况
////        //地图模式可选类型：MAP_TYPE_NORMAL,MAP_TYPE_SATELLITE,MAP_TYPE_NIGHT
//////        aMap.setMapType(AMap.MAP_TYPE_SATELLITE);// 卫星地图模式
//         MyLocationStyle myLocationStyle;
//         myLocationStyle = new MyLocationStyle();//初始化定位蓝点样式类
//////        myLocationStyle.myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE);//连续定位、且将视角移动到地图中心点，定位点依照设备方向旋转，并且会跟随设备移动。（1秒1次定位）如果不设置myLocationType，默认也会执行此种模式。
//////        myLocationStyle.interval(2000); //设置连续定位模式下的定位间隔，只在连续定位模式下生效，单次定位模式下不会生效。单位为毫秒。
//        myLocationStyle.myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATE) ;//定位一次，且将视角移动到地图中心点。
////
//         aMap.setMyLocationStyle(myLocationStyle);//设置定位蓝点的Style
//         aMap.getUiSettings().setMyLocationButtonEnabled(true);//设置默认定位按钮是否显示，非必需设置。
//        aMap.setMyLocationEnabled(true);// 设置为true表示启动显示定位蓝点，false表示隐藏定位蓝点并不进行定位，默认是false。
//        aMap.moveCamera(CameraUpdateFactory.zoomTo(19));

//        try {
//            mLocationClient = new AMapLocationClient(getApplicationContext());//初始化定位
//            //设置定位回调监听
//            mLocationClient.setLocationListener(mLocationListener);
//            mLocationOption = new AMapLocationClientOption();//初始化AMapLocationClientOption对象
//            //设置定位模式为AMapLocationMode.Hight_Accuracy，高精度模式。
//            mLocationOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
//            //获取一次定位结果：
//            //该方法默认为false。
//            mLocationOption.setOnceLocation(true);
//            //获取最近3s内精度最高的一次定位结果：
//            //设置setOnceLocationLatest(boolean b)接口为true，启动定位时SDK会返回最近3s内精度最高的一次定位结果。如果设置其为true，setOnceLocation(boolean b)接口也会被设置为true，反之不会，默认为false。
//            mLocationOption.setOnceLocationLatest(true);
//            //设置是否返回地址信息（默认返回地址信息）
//            mLocationOption.setNeedAddress(true);
//            //关闭缓存机制
//            mLocationOption.setLocationCacheEnable(false);
//            //给定位客户端对象设置定位参数
//            mLocationClient.setLocationOption(mLocationOption);
//            //启动定位
//            mLocationClient.startLocation();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

//    @Override
//    protected void onDestroy() {
//        super.onDestroy();
//        //在activity执行onDestroy时执行mMapView.onDestroy()，销毁地图
//        mMapView.onDestroy();
//    }
//    @Override
//    protected void onResume() {
//        super.onResume();
//        //在activity执行onResume时执行mMapView.onResume ()，重新绘制加载地图
//        mMapView.onResume();
//    }
//    @Override
//    protected void onPause() {
//        super.onPause();
//        //在activity执行onPause时执行mMapView.onPause ()，暂停地图的绘制
//        mMapView.onPause();
//    }
//    @Override
//    protected void onSaveInstanceState(Bundle outState) {
//        super.onSaveInstanceState(outState);
//        //在activity执行onSaveInstanceState时执行mMapView.onSaveInstanceState (outState)，保存地图当前的状态
//        mMapView.onSaveInstanceState(outState);
//    }
    }

    // WebView性能优化设置
    private void setupWebViewOptimization(WebSettings wSettings) {
        // 智能硬件加速设置
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            webView.setLayerType(View.LAYER_TYPE_HARDWARE, null);
        } else {
            webView.setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        }

        // 基础设置
        wSettings.setUseWideViewPort(true); // 优化视口
        wSettings.setLoadWithOverviewMode(true); // 优化加载
        wSettings.setRenderPriority(WebSettings.RenderPriority.HIGH); // 提高渲染优先级

        // 缓存优化设置
        wSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        wSettings.setAppCacheEnabled(true);
        wSettings.setAppCachePath(getCacheDir().getAbsolutePath());
        wSettings.setAppCacheMaxSize(50 * 1024 * 1024); // 50MB

        // 内存优化
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            wSettings.setLoadsImagesAutomatically(true);
        } else {
            wSettings.setLoadsImagesAutomatically(false);
        }

        // 其他性能优化
        wSettings.setBuiltInZoomControls(false);
        wSettings.setSupportZoom(false);
        wSettings.setDisplayZoomControls(false);
    }

    // 检查是否需要清除缓存
    private boolean shouldClearCache() {
        android.content.SharedPreferences prefs = getSharedPreferences(sharedPreferencesKey, MODE_PRIVATE);
        long lastClearTime = prefs.getLong("last_cache_clear", 0);
        long currentTime = System.currentTimeMillis();
        // 7天清除一次缓存，或者是首次启动
        return (currentTime - lastClearTime > 7 * 24 * 60 * 60 * 1000) || lastClearTime == 0;
    }

    // 标记缓存已清除
    private void markCacheCleared() {
        android.content.SharedPreferences prefs = getSharedPreferences(sharedPreferencesKey, MODE_PRIVATE);
        prefs.edit().putLong("last_cache_clear", System.currentTimeMillis()).apply();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (webView != null) {
            webView.onResume();
            webView.resumeTimers();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (webView != null) {
            webView.onPause();
            webView.pauseTimers();
        }
    }

    @Override
    protected void onDestroy() {
        if (webView != null) {
            webView.loadDataWithBaseURL(null, "", "text/html", "utf-8", null);
            webView.clearHistory();
            webView.destroy();
            webView = null;
        }
        super.onDestroy();
    }

    private static final String AMAP_PACKAGE_NAME = "com.autonavi.minimap";  // 高德地图包名

    public void handleAmapNavigation(String url) {
// 检查高德App是否安装
        if (isAppInstalled(AMAP_PACKAGE_NAME)) {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            intent.setPackage(AMAP_PACKAGE_NAME);  // 指定包名
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            try {
                startActivity(intent);  // 启动高德App
            } catch (Exception e) {
                Toast.makeText(this, "高德导航启动失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();  // 捕获异常
            }
        } else {
            // 高德App未安装，降级到Web版本或提示用户
            Toast.makeText(this, "请先安装高德地图App", Toast.LENGTH_SHORT).show();
            // 备用：打开Web链接
            String webFallbackUrl = "https://amap.com/...";  // 高德Web导航URL
            webView.loadUrl(webFallbackUrl);
        }

    }

    private boolean isAppInstalled(String packageName) {
        try {
            getPackageManager().getApplicationInfo(packageName, 0);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == LOCATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // 权限授予，重新加载页面
                webView.reload();
            }
        }
    }

}