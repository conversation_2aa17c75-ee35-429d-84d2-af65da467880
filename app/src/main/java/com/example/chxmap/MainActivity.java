package com.example.chxmap;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.GeolocationPermissions;
import android.webkit.SslErrorHandler;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebStorage;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;
import androidx.constraintlayout.widget.ConstraintLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
//import com.amap.api.location.AMapLocation;
//import com.amap.api.location.AMapLocationClient;
//import com.amap.api.location.AMapLocationClientOption;
//import com.amap.api.location.AMapLocationListener;
//import com.amap.api.maps.AMap;
//import com.amap.api.maps.CameraUpdateFactory;
//import com.amap.api.maps.MapView;
//import com.amap.api.maps.model.MyLocationStyle;
//import com.amap.api.maps.model.Text;
//import com.amap.api.services.core.ServiceSettings;


public class MainActivity extends AppCompatActivity {
    private static final int LOCATION_PERMISSION_REQUEST_CODE = 1;
    private OptimizedWebView webView;
    private ConstraintLayout webViewContainer;

//    MapView mMapView = null;
//    Button button;
//    double jd = 0;//经度
//    double wd = 0;//纬度
    //声明AMapLocationClient类对象
//    public AMapLocationClient mLocationClient = null;
    //声明AMapLocationClientOption对象
//    public AMapLocationClientOption mLocationOption = null;
    //声明定位回调监听器
//    public AMapLocationListener mLocationListener = new AMapLocationListener() {
//        @Override
//        public void onLocationChanged(AMapLocation amapLocation) {
//            TextView tx = findViewById(R.id.textView2);
//            if (amapLocation != null) {
//                if (amapLocation.getErrorCode() == 0) {
//                    jd = amapLocation.getLatitude();
//                    wd = amapLocation.getLongitude();
    ////                    tx.setText("经度：" + amapLocation.getLatitude() +  //获取纬度
    ////                            "纬度：" + amapLocation.getLongitude() +//获取经度
//                    tx.setText( "定位精度：" + amapLocation.getAccuracy() );//获取精度信息
//                    Log.e(  "AmapError" ,"经度：" + amapLocation.getLatitude() +  //获取纬度
//                            "纬度：" + amapLocation.getLongitude() +//获取经度
//                            "精度：" + amapLocation.getAccuracy() //获取精度信息
//                    );
//                    //可在其中解析amapLocation获取相应内容。
//                }else {
//                    tx.setText("失败" + "location Error, ErrCode:"
//                            + amapLocation.getErrorCode() + ", errInfo:"
//                            + amapLocation.getErrorInfo());
//                    //定位失败时，可通过ErrCode（错误码）信息来确定失败的原因，errInfo是错误信息，详见错误码表。
//                    Log.e("AmapError","location Error, ErrCode:"
//                            + amapLocation.getErrorCode() + ", errInfo:"
//                            + amapLocation.getErrorInfo());
//                }
//            }
//        }
//    };

    private String sharedPreferencesKey = "webview_prefs";
    private boolean isFirstLaunch = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 获取WebView容器
        webViewContainer = findViewById(R.id.webview_container);

        // 动态创建WebView
        createWebView();

        // 配置WebView
        if (webView != null) {
            WebSettings wSettings = webView.getSettings();
            setupWebViewConfiguration(wSettings);

            // 设置沉浸式全屏模式
            setupFullScreenMode();

            // 加载默认URL
            loadDefaultUrl();
        } else {
            Toast.makeText(this, "WebView创建失败，请重启应用", Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 设置全屏模式
     */
    private void setupFullScreenMode() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            View decorView = getWindow().getDecorView();
            int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;
            decorView.setSystemUiVisibility(uiOptions);
        }


//        button = (Button) findViewById(R.id.button);
//
//        button.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                // 通过Handler发送消息
//                webview.post(new Runnable() {
//                    @Override
//                    public void run() {
//
//                        // 注意调用的JS方法名要对应上
//                        // 调用javascript的callJS()方法
//                        String jdstr =  jd+"";
//                        String wdstr =  wd+"";
//                        String urlstr = String.format("javascript:callJS('%s,%s')",jdstr,wdstr);
//                        webview.loadUrl(urlstr);
//                    }
//                });
//
//            }
//        });
        //刷新按钮
//        Button btnsx = (Button) findViewById(R.id.sx);
//
//        btnsx.setOnClickListener(new View.OnClickListener() {
//                 public void onClick(View v) {
//                     Intent intent = new Intent(MainActivity.this, MainActivity.class);
//                     startActivity(intent);
//                 }
//             });
// 由于设置了弹窗检验调用结果,所以需要支持js对话框
        // webview只是载体，内容的渲染需要使用webviewChromClient类去实现
        // 通过设置WebChromeClient对象处理JavaScript的对话框
        //设置响应js 的Alert()函数
//        webview.setLongClickable(true);
//        webview.setOnLongClickListener(new View.OnLongClickListener() {
//            @Override
//            public boolean onLongClick(View v) {
//                Toast.makeText(MainActivity.this, "提示的内容", Toast.LENGTH_LONG).show();
//                return true;
//            }
//        });
//        webview.setWebChromeClient(new WebChromeClient() {
//        @Override
//        public boolean onJsAlert(WebView view, String url, String message, final JsResult result) {
//            AlertDialog.Builder b = new AlertDialog.Builder(MainActivity.this);
//            b.setTitle("Alert");
//            b.setMessage(message);
//            b.setPositiveButton(android.R.string.ok, new DialogInterface.OnClickListener() {
//                @Override
//                public void onClick(DialogInterface dialog, int which) {
//                    result.confirm();
//                }
//            });
//            b.setCancelable(false);
//            b.create().show();
//            return true;
//        }
//
//        });
//
//        try{
//            ServiceSettings.updatePrivacyShow(this, true, true);
//            ServiceSettings.updatePrivacyAgree(this,true);
//        } catch (Exception e){
//            e.printStackTrace();
//        }

//
//        //获取地图控件引用
//        mMapView = (MapView) findViewById(R.id.map);
//        //在activity执行onCreate时执行mMapView.onCreate(savedInstanceState)，创建地图
//        mMapView.onCreate(savedInstanceState);// 此方法必须重写
//        AMap aMap = mMapView.getMap();
////
//////        aMap.setTrafficEnabled(true);// 显示实时交通状况
////        //地图模式可选类型：MAP_TYPE_NORMAL,MAP_TYPE_SATELLITE,MAP_TYPE_NIGHT
//////        aMap.setMapType(AMap.MAP_TYPE_SATELLITE);// 卫星地图模式
//         MyLocationStyle myLocationStyle;
//         myLocationStyle = new MyLocationStyle();//初始化定位蓝点样式类
//////        myLocationStyle.myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE);//连续定位、且将视角移动到地图中心点，定位点依照设备方向旋转，并且会跟随设备移动。（1秒1次定位）如果不设置myLocationType，默认也会执行此种模式。
//////        myLocationStyle.interval(2000); //设置连续定位模式下的定位间隔，只在连续定位模式下生效，单次定位模式下不会生效。单位为毫秒。
//        myLocationStyle.myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATE) ;//定位一次，且将视角移动到地图中心点。
////
//         aMap.setMyLocationStyle(myLocationStyle);//设置定位蓝点的Style
//         aMap.getUiSettings().setMyLocationButtonEnabled(true);//设置默认定位按钮是否显示，非必需设置。
//        aMap.setMyLocationEnabled(true);// 设置为true表示启动显示定位蓝点，false表示隐藏定位蓝点并不进行定位，默认是false。
//        aMap.moveCamera(CameraUpdateFactory.zoomTo(19));

//        try {
//            mLocationClient = new AMapLocationClient(getApplicationContext());//初始化定位
//            //设置定位回调监听
//            mLocationClient.setLocationListener(mLocationListener);
//            mLocationOption = new AMapLocationClientOption();//初始化AMapLocationClientOption对象
//            //设置定位模式为AMapLocationMode.Hight_Accuracy，高精度模式。
//            mLocationOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
//            //获取一次定位结果：
//            //该方法默认为false。
//            mLocationOption.setOnceLocation(true);
//            //获取最近3s内精度最高的一次定位结果：
//            //设置setOnceLocationLatest(boolean b)接口为true，启动定位时SDK会返回最近3s内精度最高的一次定位结果。如果设置其为true，setOnceLocation(boolean b)接口也会被设置为true，反之不会，默认为false。
//            mLocationOption.setOnceLocationLatest(true);
//            //设置是否返回地址信息（默认返回地址信息）
//            mLocationOption.setNeedAddress(true);
//            //关闭缓存机制
//            mLocationOption.setLocationCacheEnable(false);
//            //给定位客户端对象设置定位参数
//            mLocationClient.setLocationOption(mLocationOption);
//            //启动定位
//            mLocationClient.startLocation();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

//    @Override
//    protected void onDestroy() {
//        super.onDestroy();
//        //在activity执行onDestroy时执行mMapView.onDestroy()，销毁地图
//        mMapView.onDestroy();
//    }
//    @Override
//    protected void onResume() {
//        super.onResume();
//        //在activity执行onResume时执行mMapView.onResume ()，重新绘制加载地图
//        mMapView.onResume();
//    }
//    @Override
//    protected void onPause() {
//        super.onPause();
//        //在activity执行onPause时执行mMapView.onPause ()，暂停地图的绘制
//        mMapView.onPause();
//    }
//    @Override
//    protected void onSaveInstanceState(Bundle outState) {
//        super.onSaveInstanceState(outState);
//        //在activity执行onSaveInstanceState时执行mMapView.onSaveInstanceState (outState)，保存地图当前的状态
//        mMapView.onSaveInstanceState(outState);
//    }
    }

    /**
     * 动态创建WebView
     * 这种方式可以避免布局文件中的WebView初始化开销
     * 并且可以更好地控制WebView的创建时机和参数
     */
    private void createWebView() {
        // 如果已存在WebView，先清理
        if (webView != null) {
            destroyWebView();
        }

        try {
            // 在独立进程中创建WebView（可选，需要在AndroidManifest中配置）
            // 这里使用当前进程创建，性能更好
            webView = new OptimizedWebView(this);

            // 设置WebView布局参数
            ConstraintLayout.LayoutParams layoutParams = new ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                ConstraintLayout.LayoutParams.MATCH_PARENT
            );
            webView.setLayoutParams(layoutParams);

            // 设置WebView ID（用于约束布局）
            webView.setId(View.generateViewId());

            // 添加到容器
            webViewContainer.addView(webView);

            android.util.Log.d("MainActivity", "WebView created dynamically");

        } catch (Exception e) {
            android.util.Log.e("MainActivity", "Error creating WebView", e);
            // 如果动态创建失败，可以考虑降级方案
            Toast.makeText(this, "WebView创建失败", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 销毁WebView
     */
    private void destroyWebView() {
        if (webView != null) {
            try {
                // 从容器中移除
                if (webViewContainer != null) {
                    webViewContainer.removeView(webView);
                }

                // 清理WebView
                if (webView instanceof OptimizedWebView) {
                    ((OptimizedWebView) webView).cleanup();
                } else {
                    webView.loadDataWithBaseURL(null, "", "text/html", "utf-8", null);
                    webView.clearHistory();
                    webView.destroy();
                }

                webView = null;
                android.util.Log.d("MainActivity", "WebView destroyed");

            } catch (Exception e) {
                android.util.Log.e("MainActivity", "Error destroying WebView", e);
            }
        }
    }

    /**
     * 重新创建WebView
     * 在某些情况下可能需要重新创建WebView来解决性能问题
     */
    public void recreateWebView() {
        String currentUrl = null;
        if (webView != null) {
            currentUrl = webView.getUrl();
        }

        destroyWebView();
        createWebView();

        // 重新配置WebView
        if (webView != null) {
            WebSettings wSettings = webView.getSettings();

            // 重新应用所有设置
            setupWebViewConfiguration(wSettings);

            // 重新加载页面
            if (currentUrl != null && !currentUrl.equals("about:blank")) {
                webView.loadUrl(currentUrl);
            } else {
                loadDefaultUrl();
            }
        }
    }

    /**
     * 加载默认URL
     */
    private void loadDefaultUrl() {
        String zy17w = "https://************:19820/";
        if (webView != null) {
            webView.loadUrl(zy17w);
        }
    }

    /**
     * 配置WebView设置（从原来的onCreate中提取）
     */
    private void setupWebViewConfiguration(WebSettings wSettings) {
        // 每次启动都清除所有数据和缓存，确保干净的启动状态
        clearAllAppDataAndCache();

        // 优化WebView性能设置
        setupWebViewOptimization(wSettings);

        // 启用javascript
        wSettings.setJavaScriptEnabled(true);
        wSettings.setGeolocationEnabled(true);
        // 开启 DOM storage 功能
        wSettings.setDomStorageEnabled(true);
        // 开启数据库存储功能
        wSettings.setDatabaseEnabled(true);
        // 设置缓存模式
        wSettings.setCacheMode(WebSettings.LOAD_DEFAULT);

        // 如果你的网站包含混合内容（http和https混合），需要设置允许加载混合内容
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webView.getSettings().setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }

        // 设置允许JS弹窗
        wSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        wSettings.setDefaultTextEncodingName("GBK");

        // 设置WebChromeClient和WebViewClient
        setupWebViewClients();
    }

    /**
     * 设置WebView客户端
     */
    private void setupWebViewClients() {
        // 设置 WebChromeClient 处理地理位置权限请求和性能监控
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onGeolocationPermissionsShowPrompt(String origin, GeolocationPermissions.Callback callback) {
                if (ContextCompat.checkSelfPermission(MainActivity.this, Manifest.permission.ACCESS_FINE_LOCATION)
                        == PackageManager.PERMISSION_GRANTED) {
                    // 如果已经授予权限，允许使用地理位置
                    callback.invoke(origin, true, false);
                } else {
                    // 请求权限
                    ActivityCompat.requestPermissions(MainActivity.this,
                            new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                            LOCATION_PERMISSION_REQUEST_CODE);
                }
            }

            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                // 地图加载进度监控
                if (newProgress == 100) {
                    // 页面加载完成，执行地图优化脚本
                    optimizeMapPerformance();
                    // 标记地图已加载
                    if (webView instanceof OptimizedWebView) {
                        ((OptimizedWebView) webView).setMapLoaded(true);
                    }
                }
            }

            @Override
            public boolean onConsoleMessage(android.webkit.ConsoleMessage consoleMessage) {
                // 监控JavaScript错误，特别是地图相关错误
                if (consoleMessage.messageLevel() == android.webkit.ConsoleMessage.MessageLevel.ERROR) {
                    android.util.Log.e("WebView", "JS Error: " + consoleMessage.message() +
                        " at " + consoleMessage.sourceId() + ":" + consoleMessage.lineNumber());
                }
                return super.onConsoleMessage(consoleMessage);
            }
        });

        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                // 忽略SSL错误，接受所有证书（不建议在生产环境中使用）
                handler.proceed();
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                if (url.startsWith("androidamap") || url.contains("amap")) {
                    handleAmapNavigation(url);
                    return true;  // 拦截URL
                }
                return super.shouldOverrideUrlLoading(view, url);
            }

            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                // 页面开始加载时的优化
                android.util.Log.d("WebView", "Page started loading: " + url);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                // 页面加载完成后注入性能优化脚本
                injectPerformanceOptimizationScript();
                android.util.Log.d("WebView", "Page finished loading: " + url);
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                android.util.Log.e("WebView", "Error loading page: " + description + " (" + errorCode + ")");
            }

            @Override
            public void onReceivedHttpError(WebView view, android.webkit.WebResourceRequest request,
                    android.webkit.WebResourceResponse errorResponse) {
                super.onReceivedHttpError(view, request, errorResponse);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    android.util.Log.e("WebView", "HTTP Error: " + errorResponse.getStatusCode() +
                        " for " + request.getUrl().toString());
                }
            }
        });
    }

    // WebView性能优化设置
    private void setupWebViewOptimization(WebSettings wSettings) {
        // 地图专用硬件加速优化
        setupHardwareAcceleration();

        // 基础设置
        wSettings.setUseWideViewPort(true); // 优化视口
        wSettings.setLoadWithOverviewMode(true); // 优化加载
        wSettings.setRenderPriority(WebSettings.RenderPriority.HIGH); // 提高渲染优先级

        // 地图操作性能优化
        setupMapOptimization(wSettings);

        // 缓存优化设置
        wSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        wSettings.setAppCacheEnabled(true);
        wSettings.setAppCachePath(getCacheDir().getAbsolutePath());
        wSettings.setAppCacheMaxSize(100 * 1024 * 1024); // 增加到100MB用于地图瓦片缓存

        // 内存优化
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            wSettings.setLoadsImagesAutomatically(true);
        } else {
            wSettings.setLoadsImagesAutomatically(false);
        }

        // 触摸和滚动优化
        setupTouchOptimization(wSettings);
    }

    // 硬件加速专门优化
    private void setupHardwareAcceleration() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // Android 5.0+ 使用硬件加速，但针对地图优化
            webView.setLayerType(View.LAYER_TYPE_HARDWARE, null);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            // Android 4.4 使用混合模式
            webView.setLayerType(View.LAYER_TYPE_HARDWARE, null);
        } else {
            // 低版本使用软件渲染
            webView.setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        }
    }

    // 地图专用优化设置
    private void setupMapOptimization(WebSettings wSettings) {
        // 禁用缩放控件，但保持缩放功能（地图通常有自己的缩放控制）
        wSettings.setBuiltInZoomControls(false);
        wSettings.setSupportZoom(true); // 保持缩放功能给地图使用
        wSettings.setDisplayZoomControls(false);

        // 优化JavaScript执行
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            // 启用WebView调试（可选，生产环境可关闭）
            WebView.setWebContentsDebuggingEnabled(false);
        }

        // 媒体播放优化（地图可能包含动画）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            wSettings.setMediaPlaybackRequiresUserGesture(false);
        }

        // 文件访问优化
        wSettings.setAllowFileAccess(true);
        wSettings.setAllowContentAccess(true);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            wSettings.setAllowFileAccessFromFileURLs(false);
            wSettings.setAllowUniversalAccessFromFileURLs(false);
        }
    }

    // 触摸和滚动优化
    private void setupTouchOptimization(WebSettings wSettings) {
        // 启用平滑滚动
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            webView.setNestedScrollingEnabled(true);
        }

        // 设置WebView为可点击和可聚焦
        webView.setClickable(true);
        webView.setFocusable(true);
        webView.setFocusableInTouchMode(true);

        // 启用长按
        webView.setLongClickable(true);
    }

    // 检查是否需要清除缓存
    private boolean shouldClearCache() {
        android.content.SharedPreferences prefs = getSharedPreferences(sharedPreferencesKey, MODE_PRIVATE);
        long lastClearTime = prefs.getLong("last_cache_clear", 0);
        long currentTime = System.currentTimeMillis();
        // 7天清除一次缓存，或者是首次启动
        return (currentTime - lastClearTime > 7 * 24 * 60 * 60 * 1000) || lastClearTime == 0;
    }

    // 标记缓存已清除
    private void markCacheCleared() {
        android.content.SharedPreferences prefs = getSharedPreferences(sharedPreferencesKey, MODE_PRIVATE);
        prefs.edit().putLong("last_cache_clear", System.currentTimeMillis()).apply();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (webView != null) {
            webView.onResume();
            webView.resumeTimers();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (webView != null) {
            webView.onPause();
            webView.pauseTimers();
        }
    }

    // 地图性能优化脚本注入
    private void optimizeMapPerformance() {
        if (webView != null) {
            // 注入地图性能优化JavaScript
            String optimizationScript =
                "javascript:(function(){" +
                "  if(typeof window.mapOptimized === 'undefined') {" +
                "    window.mapOptimized = true;" +
                "    console.log('Map performance optimization applied');" +
                "    " +
                "    // 优化地图渲染性能" +
                "    if(window.requestAnimationFrame) {" +
                "      var originalRAF = window.requestAnimationFrame;" +
                "      window.requestAnimationFrame = function(callback) {" +
                "        return originalRAF.call(window, function(timestamp) {" +
                "          try { callback(timestamp); } catch(e) { console.error('RAF error:', e); }" +
                "        });" +
                "      };" +
                "    }" +
                "    " +
                "    // 优化触摸事件处理" +
                "    document.addEventListener('touchstart', function(e) {" +
                "      e.preventDefault();" +
                "    }, {passive: false});" +
                "    " +
                "    // 禁用选择文本（避免地图操作时误选）" +
                "    document.body.style.webkitUserSelect = 'none';" +
                "    document.body.style.webkitTouchCallout = 'none';" +
                "  }" +
                "})();";

            webView.loadUrl(optimizationScript);
        }
    }

    // 注入性能优化脚本
    private void injectPerformanceOptimizationScript() {
        if (webView != null) {
            String script =
                "javascript:(function(){" +
                "  // 优化滚动性能" +
                "  document.body.style.webkitOverflowScrolling = 'touch';" +
                "  " +
                "  // 优化图片加载" +
                "  var images = document.getElementsByTagName('img');" +
                "  for(var i = 0; i < images.length; i++) {" +
                "    images[i].style.webkitTransform = 'translateZ(0)';" +
                "  }" +
                "  " +
                "  // 强制硬件加速" +
                "  document.body.style.webkitTransform = 'translateZ(0)';" +
                "  console.log('Performance optimization script injected');" +
                "})();";

            webView.loadUrl(script);
        }
    }

    @Override
    protected void onDestroy() {
        destroyWebView();
        super.onDestroy();
    }

    private static final String AMAP_PACKAGE_NAME = "com.autonavi.minimap";  // 高德地图包名

    public void handleAmapNavigation(String url) {
// 检查高德App是否安装
        if (isAppInstalled(AMAP_PACKAGE_NAME)) {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            intent.setPackage(AMAP_PACKAGE_NAME);  // 指定包名
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            try {
                startActivity(intent);  // 启动高德App
            } catch (Exception e) {
                Toast.makeText(this, "高德导航启动失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();  // 捕获异常
            }
        } else {
            // 高德App未安装，降级到Web版本或提示用户
            Toast.makeText(this, "请先安装高德地图App", Toast.LENGTH_SHORT).show();
            // 备用：打开Web链接
            String webFallbackUrl = "https://amap.com/...";  // 高德Web导航URL
            webView.loadUrl(webFallbackUrl);
        }

    }

    private boolean isAppInstalled(String packageName) {
        try {
            getPackageManager().getApplicationInfo(packageName, 0);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == LOCATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // 权限授予，重新加载页面
                webView.reload();
            }
        }
    }

}