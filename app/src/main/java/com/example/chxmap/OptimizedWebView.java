package com.example.chxmap;

import android.content.Context;
import android.graphics.Canvas;
import android.os.Build;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.webkit.WebView;

/**
 * 针对地图操作优化的WebView
 * 主要优化触摸性能和渲染性能
 */
public class OptimizedWebView extends WebView {
    
    private long lastTouchTime = 0;
    private static final long TOUCH_THROTTLE_MS = 16; // 约60fps
    private boolean isMapLoaded = false;

    public OptimizedWebView(Context context) {
        super(context);
        init();
    }

    public OptimizedWebView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public OptimizedWebView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        // 启用硬件加速
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
            setLayerType(LAYER_TYPE_HARDWARE, null);
        }
        
        // 优化滚动性能
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            setNestedScrollingEnabled(true);
        }
        
        // 设置WebView属性
        setScrollBarStyle(SCROLLBARS_INSIDE_OVERLAY);
        setScrollbarFadingEnabled(true);
        
        // 优化触摸
        setClickable(true);
        setFocusable(true);
        setFocusableInTouchMode(true);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        // 触摸事件节流，避免过于频繁的事件处理
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastTouchTime < TOUCH_THROTTLE_MS && 
            event.getAction() == MotionEvent.ACTION_MOVE) {
            return true; // 跳过过于频繁的移动事件
        }
        lastTouchTime = currentTime;
        
        try {
            return super.onTouchEvent(event);
        } catch (Exception e) {
            // 捕获可能的触摸事件异常
            android.util.Log.e("OptimizedWebView", "Touch event error", e);
            return false;
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        try {
            super.onDraw(canvas);
        } catch (Exception e) {
            // 捕获绘制异常，避免崩溃
            android.util.Log.e("OptimizedWebView", "Draw error", e);
        }
    }

    @Override
    public void onResume() {
        try {
            super.onResume();
            // 恢复时重新优化
            if (isMapLoaded) {
                postDelayed(this::reapplyOptimizations, 500);
            }
        } catch (Exception e) {
            android.util.Log.e("OptimizedWebView", "Resume error", e);
        }
    }

    @Override
    public void onPause() {
        try {
            super.onPause();
        } catch (Exception e) {
            android.util.Log.e("OptimizedWebView", "Pause error", e);
        }
    }

    /**
     * 标记地图已加载
     */
    public void setMapLoaded(boolean loaded) {
        this.isMapLoaded = loaded;
        if (loaded) {
            reapplyOptimizations();
        }
    }

    /**
     * 重新应用优化设置
     */
    private void reapplyOptimizations() {
        post(() -> {
            // 强制硬件加速
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
                setLayerType(LAYER_TYPE_HARDWARE, null);
            }
            
            // 注入优化脚本
            String script = 
                "javascript:(function(){" +
                "  if(document.body) {" +
                "    document.body.style.webkitTransform = 'translateZ(0)';" +
                "    document.body.style.webkitBackfaceVisibility = 'hidden';" +
                "    document.body.style.webkitPerspective = '1000';" +
                "  }" +
                "})();";
            loadUrl(script);
        });
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        try {
            clearCache(false); // 不清除所有缓存，只清除内存缓存
            clearHistory();
            removeAllViews();
            destroy();
        } catch (Exception e) {
            android.util.Log.e("OptimizedWebView", "Cleanup error", e);
        }
    }

    @Override
    public void destroy() {
        try {
            // 清理前先暂停
            onPause();
            super.destroy();
        } catch (Exception e) {
            android.util.Log.e("OptimizedWebView", "Destroy error", e);
        }
    }
}
