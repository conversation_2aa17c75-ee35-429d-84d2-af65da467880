# 启动时清理应用数据和缓存方案

## 方案概述

实现每次应用启动时自动清除所有应用数据和缓存，确保应用始终以"干净"状态运行，彻底解决缓存积累导致的性能问题。

## 实现原理

### 1. 启动时机
在`onCreate()`方法的最开始执行清理操作：
```java
@Override
protected void onCreate(Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    
    // 每次启动时清除所有应用数据和缓存
    clearAllAppDataAndCacheOnStartup();
    
    // 继续正常的初始化流程...
}
```

### 2. 清理范围

#### WebView相关数据
- WebView缓存和历史记录
- WebStorage数据
- WebView数据库
- WebView系统级缓存
- 客户端证书缓存

#### 应用存储数据
- 内部缓存目录 (`getCacheDir()`)
- 内部文件目录 (`getFilesDir()`)
- 代码缓存目录 (`getCodeCacheDir()`)
- 外部缓存目录 (`getExternalCacheDir()`)
- 外部文件目录 (`getExternalFilesDirs()`)

#### 数据库和配置
- 所有SQLite数据库文件
- SharedPreferences文件
- WebView相关数据库

#### 系统缓存
- 应用在系统中的缓存
- 临时文件
- 进程内存缓存

## 核心功能

### 1. 同步清理模式
```java
private void clearAllAppDataAndCacheOnStartup() {
    // 立即执行清理，阻塞启动直到完成
    clearWebViewSystemData();
    clearAppInternalStorage();
    clearAppExternalStorage();
    clearAppDatabases();
    clearAllSharedPreferences();
    clearSystemCache();
    performMemoryCleanup();
}
```

**优点**：
- 确保清理完成后再继续初始化
- 启动后立即获得干净环境

**缺点**：
- 可能延长启动时间
- 在主线程执行可能影响响应性

### 2. 异步清理模式
```java
private void clearAllAppDataAndCacheAsync() {
    Toast.makeText(this, "正在清理应用数据，请稍候...", Toast.LENGTH_SHORT).show();
    
    new Thread(() -> {
        clearAllAppDataAndCacheOnStartup();
        runOnUiThread(() -> {
            Toast.makeText(this, "应用数据清理完成", Toast.LENGTH_SHORT).show();
        });
    }).start();
}
```

**优点**：
- 不阻塞UI线程
- 启动响应更快
- 用户体验更好

**缺点**：
- 清理过程中可能存在脏数据
- 需要处理并发问题

### 3. 智能清理策略
```java
// 在onCreate中选择清理模式
boolean useAsyncCleanup = true; // 可配置

if (useAsyncCleanup) {
    clearAllAppDataAndCacheAsync();
} else {
    clearAllAppDataAndCacheOnStartup();
}
```

## 详细清理操作

### 1. WebView系统数据清理
```java
private void clearWebViewSystemData() {
    // 清除客户端证书缓存
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
        WebView.clearClientCertPreferences(null);
    }
    
    // 清除WebView数据目录
    File webViewDir = new File(getApplicationInfo().dataDir, "app_webview");
    deleteDirectoryContents(webViewDir);
    
    // 清除WebView缓存目录
    File webViewCacheDir = new File(getCacheDir(), "webview");
    deleteDirectoryContents(webViewCacheDir);
}
```

### 2. 存储清理
```java
private void clearAppInternalStorage() {
    // 清除所有内部存储目录
    deleteDirectoryContents(getCacheDir());
    deleteDirectoryContents(getFilesDir());
    
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
        deleteDirectoryContents(getCodeCacheDir());
    }
}

private void clearAppExternalStorage() {
    // 清除所有外部存储目录
    deleteDirectoryContents(getExternalCacheDir());
    
    File[] externalDirs = getExternalFilesDirs(null);
    for (File dir : externalDirs) {
        if (dir != null) {
            deleteDirectoryContents(dir);
        }
    }
}
```

### 3. 数据库清理
```java
private void clearAppDatabases() {
    // 清除数据库目录
    File dbDir = new File(getApplicationInfo().dataDir, "databases");
    deleteDirectoryContents(dbDir);
    
    // 删除特定数据库
    String[] dbNames = {"webview.db", "webviewCache.db", "Cookies"};
    for (String dbName : dbNames) {
        deleteDatabase(dbName);
    }
}
```

### 4. 内存清理
```java
private void performMemoryCleanup() {
    Runtime runtime = Runtime.getRuntime();
    long beforeUsed = runtime.totalMemory() - runtime.freeMemory();
    
    // 多次强制垃圾回收
    for (int i = 0; i < 5; i++) {
        System.gc();
        System.runFinalization();
        Thread.sleep(50);
    }
    
    long afterUsed = runtime.totalMemory() - runtime.freeMemory();
    Log.d("Memory", "释放内存: " + (beforeUsed - afterUsed) / 1024 / 1024 + "MB");
}
```

## 性能影响分析

### 1. 启动时间影响
- **同步模式**：增加200-500ms启动时间
- **异步模式**：几乎不影响启动时间

### 2. 内存使用
- **清理前**：可能有大量缓存数据占用内存
- **清理后**：内存使用降至最低水平

### 3. 存储空间
- **清理前**：缓存可能占用几十MB到几百MB
- **清理后**：存储使用降至应用本身大小

## 配置选项

### 1. 清理模式选择
```java
// 在onCreate中配置
boolean useAsyncCleanup = true; // true=异步, false=同步
```

### 2. 清理范围配置
```java
// 可以选择性启用/禁用某些清理操作
private static final boolean CLEAR_WEBVIEW_DATA = true;
private static final boolean CLEAR_INTERNAL_STORAGE = true;
private static final boolean CLEAR_EXTERNAL_STORAGE = true;
private static final boolean CLEAR_DATABASES = true;
private static final boolean CLEAR_SHARED_PREFS = true;
```

### 3. 日志级别配置
```java
// 控制清理过程的日志输出
private static final boolean ENABLE_CLEANUP_LOGS = true;
```

## 注意事项

### 1. 用户数据丢失
- **风险**：清理会删除所有用户数据和设置
- **解决**：如需保留某些数据，在清理前备份

### 2. 首次使用体验
- **影响**：每次启动都像首次使用
- **考虑**：是否需要保留某些用户偏好设置

### 3. 网络流量
- **影响**：每次都需要重新下载资源
- **优化**：考虑保留关键资源的缓存

### 4. 性能权衡
- **清理彻底**：确保性能最佳
- **启动延迟**：可能影响用户体验

## 使用建议

### 1. 开发阶段
- 使用同步清理模式，确保测试环境干净
- 启用详细日志，监控清理效果

### 2. 生产环境
- 推荐使用异步清理模式
- 根据用户反馈调整清理策略

### 3. 特殊场景
- 地图应用：建议启用完整清理
- 数据敏感应用：考虑保留用户设置
- 离线应用：可能需要保留某些缓存

这种启动时清理方案可以确保您的WebView地图应用每次都以最佳状态运行，彻底解决缓存积累导致的性能问题。
