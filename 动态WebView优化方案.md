# 动态创建WebView优化方案

## 方案概述

将WebView从布局文件中移除，改为在Activity的onCreate方法中动态创建。这种方式可以带来多个性能优势，特别是对于地图应用。

## 优势分析

### 1. 启动性能优化
**传统方式问题**：
- 布局文件中的WebView在setContentView时就开始初始化
- WebView初始化是重量级操作，会阻塞UI线程
- 即使WebView还未配置，也会消耗资源

**动态创建优势**：
- 可以控制WebView的创建时机
- 在Activity完全初始化后再创建WebView
- 可以在后台线程准备WebView配置

### 2. 内存管理优化
**更好的生命周期控制**：
```java
// 完全控制WebView的创建和销毁
private void createWebView() {
    if (webView != null) {
        destroyWebView(); // 确保旧实例被清理
    }
    webView = new OptimizedWebView(this);
    // 精确配置...
}

private void destroyWebView() {
    if (webView != null) {
        webViewContainer.removeView(webView);
        ((OptimizedWebView) webView).cleanup();
        webView = null;
    }
}
```

**内存泄漏预防**：
- 确保WebView从视图树中完全移除
- 调用专门的cleanup方法
- 避免Context引用泄漏

### 3. 配置灵活性
**动态配置能力**：
```java
private void setupWebViewConfiguration(WebSettings wSettings) {
    // 根据运行时条件动态配置
    if (shouldClearCache()) {
        webView.clearCache(true);
        WebStorage.getInstance().deleteAllData();
        markCacheCleared();
    }
    
    // 智能优化设置
    setupWebViewOptimization(wSettings);
    setupWebViewClients();
}
```

### 4. 错误恢复能力
**WebView重建机制**：
```java
public void recreateWebView() {
    String currentUrl = webView != null ? webView.getUrl() : null;
    
    destroyWebView();
    createWebView();
    
    if (webView != null) {
        setupWebViewConfiguration(webView.getSettings());
        if (currentUrl != null && !currentUrl.equals("about:blank")) {
            webView.loadUrl(currentUrl);
        }
    }
}
```

## 实现细节

### 1. 布局文件简化
```xml
<!-- 原来的复杂WebView配置 -->
<WebView
    android:id="@+id/wv"
    android:layout_width="match_parent"
    android:layout_height="match_parent" />

<!-- 简化为容器 -->
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/webview_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent" />
```

### 2. 动态创建逻辑
```java
private void createWebView() {
    try {
        webView = new OptimizedWebView(this);
        
        // 设置布局参数
        ConstraintLayout.LayoutParams layoutParams = 
            new ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                ConstraintLayout.LayoutParams.MATCH_PARENT
            );
        webView.setLayoutParams(layoutParams);
        
        // 生成唯一ID
        webView.setId(View.generateViewId());
        
        // 添加到容器
        webViewContainer.addView(webView);
        
    } catch (Exception e) {
        Log.e("MainActivity", "Error creating WebView", e);
        // 错误处理和降级方案
    }
}
```

### 3. 配置模块化
```java
private void setupWebViewConfiguration(WebSettings wSettings) {
    // 缓存管理
    manageCacheSettings();
    
    // 性能优化
    setupWebViewOptimization(wSettings);
    
    // 基础设置
    configureBasicSettings(wSettings);
    
    // 客户端设置
    setupWebViewClients();
}
```

## 性能提升效果

### 1. 启动时间优化
- **优化前**：Activity启动时间包含WebView初始化开销
- **优化后**：Activity快速启动，WebView异步创建

### 2. 内存使用优化
- **优化前**：WebView可能存在内存泄漏
- **优化后**：精确的内存管理，确保完全释放

### 3. 错误恢复能力
- **优化前**：WebView出错需要重启应用
- **优化后**：可以动态重建WebView，无需重启

### 4. 配置灵活性
- **优化前**：配置固化在布局和onCreate中
- **优化后**：可以根据运行时状态动态调整

## 最佳实践

### 1. 异步创建（可选）
```java
private void createWebViewAsync() {
    new AsyncTask<Void, Void, OptimizedWebView>() {
        @Override
        protected OptimizedWebView doInBackground(Void... voids) {
            return new OptimizedWebView(MainActivity.this);
        }
        
        @Override
        protected void onPostExecute(OptimizedWebView webView) {
            MainActivity.this.webView = webView;
            setupWebViewInMainThread();
        }
    }.execute();
}
```

### 2. 状态保存和恢复
```java
@Override
protected void onSaveInstanceState(Bundle outState) {
    super.onSaveInstanceState(outState);
    if (webView != null) {
        outState.putString("webview_url", webView.getUrl());
    }
}

@Override
protected void onRestoreInstanceState(Bundle savedInstanceState) {
    super.onRestoreInstanceState(savedInstanceState);
    String savedUrl = savedInstanceState.getString("webview_url");
    if (savedUrl != null && webView != null) {
        webView.loadUrl(savedUrl);
    }
}
```

### 3. 内存监控
```java
private void monitorWebViewMemory() {
    Runtime runtime = Runtime.getRuntime();
    long usedMemory = runtime.totalMemory() - runtime.freeMemory();
    Log.d("Memory", "WebView memory usage: " + usedMemory / 1024 / 1024 + "MB");
    
    // 如果内存使用过高，考虑重建WebView
    if (usedMemory > 200 * 1024 * 1024) { // 200MB
        recreateWebView();
    }
}
```

## 注意事项

### 1. Context引用
- 确保WebView使用Activity Context
- 避免使用Application Context（可能导致功能异常）

### 2. 生命周期管理
- 在onDestroy中确保WebView完全销毁
- 在onPause/onResume中正确管理WebView状态

### 3. 异常处理
- WebView创建可能失败，需要有降级方案
- 捕获所有可能的异常，避免应用崩溃

### 4. 线程安全
- WebView操作必须在主线程进行
- 如果使用异步创建，注意线程切换

## 总结

动态创建WebView是一个有效的性能优化方案，特别适合：
- 复杂的WebView应用（如地图应用）
- 需要精确内存管理的场景
- 需要错误恢复能力的应用
- 对启动性能有高要求的应用

通过这种方式，可以获得更好的性能表现和更强的可控性。
